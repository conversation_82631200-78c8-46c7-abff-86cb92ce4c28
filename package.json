{"name": "arco-design-pro-vue", "description": "Arco Design Pro for Vue", "version": "1.0.0", "private": true, "author": "ArcoDesign Team", "license": "MIT", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@arco-design/web-vue": "^2.44.7", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/vue-3": "^2.11.5", "@vueuse/core": "^9.3.0", "arco-design-pro-vue": "^2.7.3", "axios": "^1.7.9", "dayjs": "^1.11.5", "echarts": "^5.4.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.23", "pinia-plugin-persistedstate": "^4.2.0", "query-string": "^8.0.3", "sortablejs": "^1.15.0", "vue": "^3.2.40", "vue-echarts": "^6.2.3", "vue-i18n": "^9.2.2", "vue-router": "^4.0.14", "yarn": "^1.22.22"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@types/lodash": "^4.14.186", "@types/mockjs": "^1.0.7", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.0", "@vitejs/plugin-vue": "^3.1.2", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/babel-plugin-jsx": "^1.1.1", "consola": "^2.15.3", "cross-env": "^7.0.3", "husky": "^8.0.1", "less": "^4.1.3", "mockjs": "^1.1.0", "postcss-html": "^1.5.0", "prettier": "^2.7.1", "rollup": "^3.9.1", "rollup-plugin-visualizer": "^5.8.2", "typescript": "^4.8.4", "unplugin-vue-components": "^0.24.1", "vite": "^3.2.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-svg-loader": "^3.6.0", "vue-tsc": "^1.0.14"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}}