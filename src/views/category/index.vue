<template>
  <div class="page-container">
    <div class="box">
      <div class="box-header">
        <h2>栏目管理</h2>
        <p>管理网站的栏目分类</p>
      </div>

      <div class="box-main">
        <div class="toolbar">
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <icon-plus />
            </template>
            新增栏目
          </a-button>
          <a-button @click="handleExpand" style="margin-left: 12px">
            <template #icon>
              <icon-expand />
            </template>
            展开全部
          </a-button>
          <a-button @click="handleCollapse" style="margin-left: 12px">
            <template #icon>
              <icon-shrink />
            </template>
            收起全部
          </a-button>
          <a-button @click="handleRefresh" style="margin-left: 12px">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </div>

        <a-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :loading="loading"
          :pagination="false"
          row-key="id"
          :default-expand-all-rows="true"
          :scroll="{ x: 1200 }"
          :expandable="{
            isLeaf: (record) => !record.children || record.children.length === 0
          }"
        >
          <template #name="{ record }">
            <span :style="{ paddingLeft: getIndentStyle(record) }">
              {{ record.name }}
            </span>
          </template>
          <template #slug="{ record }">
            <a-tag color="blue">{{ record.slug }}</a-tag>
          </template>
          <template #status="{ record }">
            <a-switch
              v-model="record.status"
              :checked-value="1"
              :unchecked-value="0"
              @change="handleStatusChange(record)"
              :loading="record.statusLoading"
            />
          </template>
          <template #sort_order="{ record }">
            <a-input-number
              v-model="record.sort_order"
              :min="0"
              :max="255"
              size="small"
              style="width: 80px"
              @change="handleSortChange(record)"
            />
          </template>
          <template #operations="{ record }">
            <a-space>
              <a-button type="text" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="text" size="small" @click="handleAddChild(record)">
                添加子栏目
              </a-button>
              <a-button type="text" size="small" status="danger" @click="handleDelete(record)">
                删除
              </a-button>
            </a-space>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      width="600px"
      :on-before-ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitLoading"
      unmountOnClose
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="栏目名称" field="name">
          <a-input v-model="formData.name" placeholder="请输入栏目名称" />
        </a-form-item>

        <a-form-item label="URL标识" field="slug">
          <a-input v-model="formData.slug" placeholder="请输入URL友好标识" />
        </a-form-item>

        <a-form-item label="父级栏目" field="parent_id">
          <a-tree-select
            v-model="formData.parent_id"
            :data="categoryTreeData"
            :field-names="{ key: 'id', title: 'name', children: 'children' }"
            placeholder="请选择父级栏目（不选择为顶级栏目）"
            allow-clear
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="排序" field="sort_order">
              <a-input-number
                v-model="formData.sort_order"
                :min="0"
                :max="255"
                placeholder="排序值"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" field="status">
              <a-select v-model="formData.status" placeholder="请选择状态">
                <a-option :value="1">显示</a-option>
                <a-option :value="0">隐藏</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="模板" field="template">
          <a-input v-model="formData.template" placeholder="请输入模板名称（可选）" />
        </a-form-item>

        <a-form-item label="关键词" field="keywords">
          <a-textarea
            v-model="formData.keywords"
            placeholder="请输入SEO关键词"
            :rows="2"
          />
        </a-form-item>

        <a-form-item label="描述" field="description">
          <a-textarea
            v-model="formData.description"
            placeholder="请输入栏目描述"
            :rows="5"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { Modal } from '@arco-design/web-vue'
import { IconPlus, IconExpand, IconShrink, IconRefresh } from '@arco-design/web-vue/es/icon'
import {
  getCategoryList,
  createCategory,
  updateCategory,
  deleteCategory,
  updateCategoryStatus,
  updateCategorySort,
  type CategoryItem,
  type CategoryFormData
} from '@/api/category'

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '栏目名称',
    slotName: 'name',
    width: 100,
  },
  {
    title: 'URL标识',
    slotName: 'slug',
    width: 150,
  },
  {
    title: '模板',
    dataIndex: 'template',
    width: 120,
  },
  {
    title: '状态',
    slotName: 'status',
    width: 80,
  },
  {
    title: '排序',
    slotName: 'sort_order',
    width: 100,
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 150,
    fixed: 'right',
  },
]

// 响应式数据
const tableRef = ref()
const formRef = ref()
const tableData = ref<CategoryItem[]>([])
const loading = ref(false)
const modalVisible = ref(false)
const submitLoading = ref(false)
const editingId = ref<number | null>(null)

// 表单数据
const formData = reactive({
  name: '',
  slug: '',
  parent_id: 0,
  template: '',
  keywords: '',
  description: '',
  sort_order: 0,
  status: 1,
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入栏目名称' },
    { minLength: 1, maxLength: 50, message: '栏目名称长度为1-50个字符' }
  ],
  slug: [
    { required: true, message: '请输入URL标识' },
    {
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: 'URL标识只能包含字母、数字、下划线和横线'
    },
    { minLength: 1, maxLength: 60, message: 'URL标识长度为1-60个字符' }
  ],
  sort_order: [
    { required: true, message: '请输入排序值' },
    { type: 'number', min: 0, max: 255, message: '排序值范围为0-255' }
  ],
  status: [
    { required: true, message: '请选择状态' }
  ]
}

// 计算属性
const modalTitle = computed(() => editingId.value ? '编辑栏目' : '新增栏目')

// 构建树形选择数据（排除当前编辑的节点及其子节点）
const categoryTreeData = computed(() => {
  const buildTree = (items: CategoryItem[], excludeId?: number): any[] => {
    return items
      .filter(item => item.id !== excludeId)
      .map(item => ({
        id: item.id,
        name: item.name,
        children: item.children ? buildTree(item.children, excludeId) : []
      }))
  }

  // 添加根节点选项
  const treeData = [
    { id: 0, name: '顶级栏目', children: [] },
    ...buildTree(tableData.value, editingId.value || undefined)
  ]

  return treeData
})

// 获取缩进样式
const getIndentStyle = (record: CategoryItem) => {
  // 根据层级计算缩进
  const level = getNodeLevel(record, tableData.value, 0)
  return `${level * 20}px`
}

// 获取节点层级
const getNodeLevel = (target: CategoryItem, nodes: CategoryItem[], level: number): number => {
  for (const node of nodes) {
    if (node.id === target.id) {
      return level
    }
    if (node.children && node.children.length > 0) {
      const childLevel = getNodeLevel(target, node.children, level + 1)
      if (childLevel > -1) {
        return childLevel
      }
    }
  }
  return -1
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const res = await getCategoryList()
    if (res.code === 200) {
      tableData.value = res.data.list
    }
  } catch (error) {
    console.error('获取栏目数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    slug: '',
    parent_id: 0,
    template: '',
    keywords: '',
    description: '',
    sort_order: 0,
    status: 1,
  })
  editingId.value = null
  formRef.value?.clearValidate()
}

// 新增栏目
const handleAdd = () => {
  resetForm()
  modalVisible.value = true
}

// 添加子栏目
const handleAddChild = (record: CategoryItem) => {
  resetForm()
  formData.parent_id = record.id
  modalVisible.value = true
}

// 编辑栏目
const handleEdit = (record: CategoryItem) => {
  resetForm()
  editingId.value = record.id
  Object.assign(formData, {
    name: record.name,
    slug: record.slug,
    parent_id: record.parent_id,
    template: record.template || '',
    keywords: record.keywords || '',
    description: record.description || '',
    sort_order: record.sort_order,
    status: record.status,
  })
  modalVisible.value = true
}

// 刷新数据
const handleRefresh = () => {
  fetchData()
}

// 展开全部
const handleExpand = () => {
  tableRef.value?.expandAll()
}

// 收起全部
const handleCollapse = () => {
  tableRef.value?.collapseAll()
}

// 状态切换
const handleStatusChange = async (record: CategoryItem) => {
  record.statusLoading = true
  try {
    const res = await updateCategoryStatus(record.id, record.status)
    if (res.code !== 200) {
      // 恢复原状态
      record.status = record.status === 1 ? 0 : 1
    }
  } catch (error) {
    // 恢复原状态
    record.status = record.status === 1 ? 0 : 1
  } finally {
    record.statusLoading = false
  }
}

// 排序更改
const handleSortChange = async (record: CategoryItem) => {
  try {
    const res = await updateCategorySort(record.id, record.sort_order)
    if (res.code === 200) {
      fetchData() // 重新获取数据以更新排序
    } else {
      fetchData() // 恢复原数据
    }
  } catch (error) {
    fetchData() // 恢复原数据
  }
}

// 删除栏目
const handleDelete = (record: CategoryItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除栏目"${record.name}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        const res = await deleteCategory(record.id)
        if (res.code === 200) {
          fetchData()
        }
      } catch (error) {
        console.error('删除失败:', error)
      }
    }
  })
}

// 提交表单
const handleSubmit = async ( done: (closed: boolean) => void) => {
  try {
    // Ant Design Vue 的 validate() 方法：
    // - 验证成功时返回 undefined
    // - 验证失败时抛出错误
    await formRef.value?.validate()
  } catch (error) {
    // 表单验证失败，直接返回，不关闭弹窗
    return false;
  }

  submitLoading.value = true

  try {
    let res
    if (editingId.value) {
      res = await updateCategory(editingId.value, formData)
    } else {
      res = await createCategory(formData)
    }

    if (res.code === 200) {
      // API请求成功，刷新数据，返回 true 允许弹窗关闭
      fetchData()
      done(true)
      return true
    } else {
      // API返回错误码，返回 false 阻止弹窗关闭
      return false
    }
  } catch (error) {
    // API请求异常，返回 false 阻止弹窗关闭
    console.error('API请求失败:', error)
    return false
  } finally {
    setTimeout(() => {
      submitLoading.value = false
    }, 3000)
  }
}

// 取消操作
const handleCancel = () => {
  console.log('取消操作')
  modalVisible.value = false
  resetForm()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="less" scoped>
.page-container {
  padding: 20px;

  .box {
    border-radius: 8px;
    width: 100%;
    background-color: var(--color-bg-1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .box-header {
    padding: 24px 24px 0;
    border-bottom: 1px solid var(--color-border-2);
    margin-bottom: 0;

    h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--color-text-1);
    }

    p {
      margin: 0 0 24px 0;
      color: var(--color-text-3);
      font-size: 14px;
    }
  }

  .box-main {
    padding: 24px;
  }

  .toolbar {
    margin-bottom: 16px;
  }
}

// 表格内的开关样式
:deep(.arco-switch) {
  &.arco-switch-checked {
    background-color: #1890ff !important;
  }
}

// 树形表格缩进样式
:deep(.arco-table-td) {
  .arco-table-cell {
    padding: 8px 16px;
  }
}
</style>
