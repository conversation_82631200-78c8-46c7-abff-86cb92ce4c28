<template>
  <div class="page-container">
    <div class="box">
      <div class="box-header">
        <a-button type="text" @click="handleBack"><icon-arrow-left />返回上一页</a-button>
      </div>
      <div class="box-main">
        <div class="h3">基本信息<span>保存后生效</span></div>
        <div class="box-content">
          <a-form :model="form">
            <a-row :gutter="16">
              <!-- 左侧 -->
              <a-col :span="12">
                <a-row :gutter="16">
                  <a-col :span="24" v-if="form.id">
                    <a-form-item field="id" label="ID" label-col-flex="100px">
                      <a-input v-model="form.id" placeholder="请输入ID" disabled
                        style="color: var(--color-neutral-6);font-size: 12px;" />
                      <template #extra>
                        <div>当前为编辑状态</div>
                      </template>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item field="title" label="标题" label-col-flex="100px">
                      <a-input v-model="form.title" placeholder="请输入标题" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item field="title" label="行业分类" label-col-flex="100px">
                      <a-select v-model="form.category_id" placeholder="请选择行业分类" :options="form.categoryOption">
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item field="status" label="状态" label-col-flex="100px">
                      <a-switch type="round" :checked-value="1" v-model="form.status" :unchecked-value="0" />
                    </a-form-item>
                  </a-col>
                </a-row>

              </a-col>
              <!-- 右侧 -->
              <a-col :span="12">
                <Tiptap v-model="form.content" />
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24" style="text-align: center; margin-top: 20px;">
                <a-button type="primary" @click="handleSubmit">{{ form.id ? '更新文章' : '发布新文章' }}</a-button>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 请求接口
import { getList, getInfo, save, getCategoryList } from '@/api/article';

const form = ref({
  id: undefined,
  title: '',
  content: '<p>I\'m running Tiptap with Vue.js. 🎉</p>',
  status: false,
  category_id: undefined,
  categoryOption: undefined
})

const router = useRouter()
const route = useRoute()

// 提交表单
const handleSubmit = async () => {
  try {
    if (form.value.id) {
      // 有ID，更新文章
      await save(form.value);
    } else {
      // 无ID，新建文章
      await save(form.value);
    }
  } catch (error) {
    console.error(error);
  }
}

//挂载
onMounted(() => {
  // 从路由参数中获取初始值
  const id = route.query.id;
  if (id) {
    getInfo({ id }).then(res => {
      form.value = res.data;
    }).catch(err => {
      console.error(err);
    });
  }

  getCategoryList().then(res => {
    form.value.categoryOption = res.data.map((item: any) => ({
      label: item.name,
      value: item.id
    }));
  }).catch(err => {
    console.error(err);
  });
})

// 返回
const handleBack = () => {
  router.back()
}


</script>

<style lang="less" scoped>
.page-container {
  padding: 10px;

  .h3 {
    font-weight: 300;
    font-size: 17px;
    line-height: 30px;
    margin-bottom: 15px;
    margin-top: 30px;
    color: var(--color-neutral-10);

    span {
      font-size: 12px;
      margin-left: 8px;
      color: var(--color-neutral-6);
    }
  }

  .box {
    padding: 10px;
    border-radius: 3px;
    width: 100%;
    background-color: var(--color-bg-1);
    min-height: calc(100vh - 160px);
  }

  .box-header {
    margin-bottom: 20px;
  }

  .box-main {
    padding: 20px;
    padding-top: 0;
  }
}
</style>
