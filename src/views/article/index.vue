<template>
  <div class="page-container">
    <div class="box">
      <div class="box-header">
        <div style="display: flex; justify-content: space-between; width: 100%; align-items: center;">
          <a-space size="large">
            <!-- 搜索 -->
            <a-input-group>
              <a-select :style="{ width: '128px' }" placeholder="请选择" v-model="pageConf.field" allow-search
                :options="pageConf.fieldOption">
              </a-select>
              <a-input :style="{ width: '298px' }" placeholder="请输入" v-model="pageConf.keyword" allow-clear
                icon="search" @press-enter="nextPage(true)">
                <template #prefix>
                  <icon-search />
                </template>
              </a-input>
            </a-input-group>
            <!-- 日期 -->
            <a-input-group>
              <a-range-picker :style="{ width: '328px' }" v-model="pageConf.timeArr" @change="changeTime" />
            </a-input-group>
            <!-- 按钮 -->
            <a-button type="primary" @click="nextPage(true)">搜索</a-button>
          </a-space>
          <!-- 修改工具区样式 -->
          <a-space size="large">
            <a-button type="outline" @click="handleAdd"><icon-plus />发布</a-button>
          </a-space>
        </div>
      </div>
      <div class="box-main">
        <!-- 数据列表 -->
        <a-table :columns="columns" :data="pageConf.list" :bordered="{ cell: true }" stripe hover class="custom-table"
          :loading="pageConf.tableLoading" :pagination="{
            total: pageConf.total,
            current: pageConf.page,
            pageSize: pageConf.limit,
            showTotal: true,
            showJumper: true,
            showPageSize: true,
            pageSizeOptions: pageConf.limitOption
          }" @page-change="onPageChange" @page-size-change="nextPage(true)">
          <!-- 自定义渲染列  用户信息 -->
          <template #user_info="{ record }">
            <a-space>
              <a-avatar>
                <img :src="record.user?.avatar"/>
              </a-avatar>
              <span>{{ record.user?.nickname }}</span>
            </a-space>
          </template>
          <!-- 自定义渲染列  状态 -->
          <template #status="{ record }">
            <a-switch type="round" :checked-value="1" v-model="record.status" :unchecked-value="0"
              :loading="pageConf.tableLoading" @change="handleStatus(record)" />
          </template>
          <!-- 自定义渲染列  操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="outline" @click="handleEdit(record)">编辑</a-button>
              <a-button type="text" @click="handleDelete(record)" style="color:var(--color-neutral-6)">删除</a-button>
            </a-space>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
// 请求接口
import { getList, deleteArticle } from '@/api/article';

//页面参数
const pageConf = ref({
  page: 1,//当前页
  limit: 10,//每页条数
  limitOption: [10, 15, 20, 50, 100],//每页条数Option
  total: 0,//总条数
  fieldOption: [],//搜索字段Option
  field: '',//搜索字段
  keyword: '',//关键词
  timeArr: [] as Date[],//日期范围字符串
  time: "",//日期范围
  list: [],//列表数据
  tableLoading: false,//表格loading
})

const router = useRouter()
const route = useRoute()

// 初始化
const nextPage = (initPage = false) => {
  pageConf.value.tableLoading = true;
  if (initPage) {
    pageConf.value.page = 1;
    pageConf.value.limit = 10;
  }

  const params = {
    page: pageConf.value.page,
    limit: pageConf.value.limit,
    field: pageConf.value.field,
    keyword: pageConf.value.keyword,
    time: pageConf.value.time
  }

  // 更新路由参数
  router.push({
    query: params
  })

  getList(params).then((res) => {
    // 数据获取成功后 code= 200
    pageConf.value.list = res.data.list
    pageConf.value.total = res.data.total
    pageConf.value.limit = res.data.limit
    pageConf.value.fieldOption = res.data.fields
  }).finally(() => {
    pageConf.value.tableLoading = false;
  })
}

// 添加页码改变处理函数
const onPageChange = (page: number) => {
  pageConf.value.page = page;
  nextPage();
}

// 日期范围改变
const changeTime = (dates: any) => {
  pageConf.value.timeArr = dates;
  pageConf.value.time = dates.join(',');

  console.log(pageConf.value)
}

// 状态改变
const handleStatus = (record: any) => {
  console.log(record)
}

// 编辑
const handleEdit = (record: any) => {
  router.push({
    path: '/article/details',
    query: {
      id: record.id
    }
  })
}

// 删除
const handleDelete = (record: any) => {
  Modal.warning({
    title: '确认删除',
    content: '确定要删除这条文章吗？删除后不可恢复。',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      deleteArticle({ id: record.id }).then((res) => {
        nextPage(true);
      }).catch((err) => {
        Message.error('删除失败');
      });
    }
  });
}

// 发布
const handleAdd = () => {
  router.push({
    name: 'ArticleDetails'
  })
}

//挂载
onMounted(() => {
  // 从路由参数中获取初始值
  const query = route.query
  if (Object.keys(query).length > 0) {
    pageConf.value.page = Number(query.page) || 1
    pageConf.value.limit = Number(query.limit) || 10
    pageConf.value.field = query.field as string || ''
    pageConf.value.keyword = query.keyword as string || ''
    pageConf.value.time = query.time as string || ''
    if (query.time && typeof query.time === 'string') {
      // 将字符串转换为日期数组
      const timeArr = query.time.split(',')
      pageConf.value.timeArr = timeArr.map(dateStr => new Date(dateStr))
    }
  }
  nextPage()
})

const formModel = ref({
  userId: '',
  keyword: '',
  dateRange: []
})

const pageConfig = ref({
  total: 100,
  pageSize: 10,
  current: 1,
  showTotal: true,
  showSizeChanger: true,
})

// 添加 TableColumnData 类型导入
import type { TableColumnData } from '@arco-design/web-vue';

// 修改 columns 的定义
const columns = ref<TableColumnData[]>([
  {
    title: 'ID',
    dataIndex: 'id',
    width: 100,
  },
  {
    title: '标题',
    dataIndex: 'title',
    width: 500,
    tooltip: true,
    ellipsis: true,
  },
  {
    title: '用户',
    slotName: 'user_info',
    width: 220,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '状态',
    slotName: 'status',
    width: 120,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 220,
  },
  {
    title: '操作',
    slotName: 'action',
  },
])

const onSelect = (dates: any) => {
  console.log(dates)
}

const onOk = (dates: any) => {
  console.log(dates)
}

</script>

<style lang="less" scoped>
.page-container {
  padding: 10px;

  .box {
    padding: 10px;
    border-radius: 3px;
    width: 100%;
    background-color: var(--color-bg-1);
    min-height: calc(100vh - 160px);
  }

  .box-header {
    margin-bottom: 20px;
  }

  // 添加自定义表格样式
  :deep(.custom-table) {

    // 表头样式
    .arco-table-th {
      background-color: var(--color-fill-2);
      font-weight: 600;
      padding: 12px 16px;
    }

    // 单元格样式
    .arco-table-td {
      padding: 12px 16px;
    }

    // 条纹样式
    .arco-table-tr:nth-child(even) {
      background-color: var(--color-fill-1);
    }

    // hover效果
    .arco-table-tr:hover {
      td {
        background-color: var(--color-fill-2);
      }
    }
  }
}
</style>
