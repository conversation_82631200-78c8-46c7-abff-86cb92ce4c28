<template>
  <div class="login-form-wrapper">
    <div class="login-form-title">登录</div>
    <div class="login-form-sub-title">CMS 后台管理系统</div>
    <div class="login-form-error-msg">{{ errorMessage }}</div>
    <a-form ref="loginForm" :model="userInfo" class="login-form" layout="vertical" @submit="handleSubmit">
      <a-form-item field="username" allow-clear hide-label>
        <a-input v-model="userInfo.username" placeholder="请输入用户名">
          <template #prefix>
            <icon-user />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item field="password" hide-label>
        <a-input-password v-model="userInfo.password" placeholder="请输入密码" allow-clear>
          <template #prefix>
            <icon-lock />
          </template>
        </a-input-password>
      </a-form-item>
      <a-space :size="16" direction="vertical">
        <div class="login-form-password-actions">
          <a-checkbox checked="rememberPassword" :model-value="loginConfig.rememberPassword"
            @change="setRememberPassword as any">
            记住密码
          </a-checkbox>
        </div>
        <a-button type="primary" html-type="submit" long :loading="loading">
          登录
        </a-button>
      </a-space>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useStorage } from '@vueuse/core';
import { useUserStore } from '@/store';
import useLoading from '@/hooks/loading';

const router = useRouter();
const errorMessage = ref('');
const { loading, setLoading } = useLoading();
const userStore = useUserStore();

const loginConfig = useStorage('login-config', {
  rememberPassword: true,
  username: 'admin', // 演示默认值
  password: '123456', // 默认密码
});
const userInfo = reactive({
  username: loginConfig.value.username,
  password: loginConfig.value.password,
});

// 登录
const handleSubmit = async () => {
  setLoading(true);
  try {
    const res = await userStore.login(userInfo);

    // 如果勾选了记住密码，保存登录信息到 localStorage
    if (loginConfig.value.rememberPassword) {
      loginConfig.value.username = userInfo.username;
      loginConfig.value.password = userInfo.password;
    } else {
      // 如果取消勾选，则清除保存的登录信息
      loginConfig.value.username = '';
      loginConfig.value.password = '';
    }

    const { redirect, ...othersQuery } = router.currentRoute.value.query;
    router.push({
      name: redirect && redirect !== 'login' ? (redirect as string) : 'Workplace',
      query: {
        ...othersQuery,
      },
    });
  } catch (error) {
    errorMessage.value = error as string || '登录失败，请检查用户名和密码';
  } finally {
    setLoading(false);
  }
};

// 记住密码 
const setRememberPassword = (value: boolean) => {
  loginConfig.value.rememberPassword = value;
};

</script>

<style lang="less" scoped>
.login-form {
  &-wrapper {
    width: 320px;
  }

  &-title {
    color: var(--color-text-1);
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }

  &-sub-title {
    color: var(--color-text-3);
    font-size: 16px;
    line-height: 24px;
  }

  &-error-msg {
    height: 32px;
    color: rgb(var(--red-6));
    line-height: 32px;
  }

  &-password-actions {
    display: flex;
    justify-content: space-between;
  }

  &-register-btn {
    color: var(--color-text-3) !important;
  }
}
</style>
