<template>
  <div class="page-container">
    <div class="box">
      <div class="box-header">
        <div style="display: flex; justify-content: space-between; width: 100%; align-items: center;">
          <a-space size="large">
            <!-- 搜索 -->
            <a-input-group>
              <a-select :style="{ width: '128px' }" placeholder="请选择" v-model="pageConf.field" allow-search
                :options="pageConf.fieldOption">
              </a-select>
              <a-input :style="{ width: '298px' }" placeholder="请输入" v-model="pageConf.keyword" allow-clear
                icon="search" @press-enter="nextPage(true)">
                <template #prefix>
                  <icon-search />
                </template>
              </a-input>
            </a-input-group>
            <!-- 日期 -->
            <a-input-group>
              <a-range-picker :style="{ width: '328px' }" v-model="pageConf.timeArr" @change="changeTime" />
            </a-input-group>
            <!-- 按钮 -->
            <a-button type="primary" @click="nextPage(true)">搜索</a-button>
          </a-space>
          <!-- 修改工具区样式 -->
          <a-space size="large">
            <a-button type="outline" @click="handleAdd"><icon-plus />添加管理员</a-button>
          </a-space>
        </div>
      </div>
      <div class="box-main">
        <!-- 数据列表 -->
        <a-table :columns="columns" :data="pageConf.list" :bordered="{ cell: true }" stripe hover class="custom-table"
          :loading="pageConf.tableLoading" :pagination="{
            total: pageConf.total,
            current: pageConf.page,
            pageSize: pageConf.limit,
            showTotal: true,
            showJumper: true,
            showPageSize: true,
            pageSizeOptions: pageConf.limitOption
          }" @page-change="onPageChange" @page-size-change="nextPage(true)">
          <!-- 自定义渲染列  用户信息 -->
          <template #userInfo="{ record }">
            <a-space>
              <a-avatar shape="square" :size="40" :image-url="record.avatar">
                <icon-user v-if="!record.avatar" />
              </a-avatar>
              <div class="user-detail">
                <div class="nickname">{{ record.nickname }}</div>
                <div class="username">{{ record.username }}</div>
              </div>
            </a-space>
          </template>
          <!-- 自定义渲染列  状态 -->
          <template #status="{ record }">
            <a-switch type="round" :checked-value="true" v-model="record.status" :unchecked-value="false"
              :loading="pageConf.tableLoading" @change="handleStatus(record)" />
          </template>
          <!-- 自定义渲染列  操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="text" @click="handleEdit(record)">重置密码</a-button>
              <a-popconfirm content="确定要删除该管理员吗？" type="warning" position="left" @ok="handleDelete(record)">
                <a-button type="text" status="danger">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 添加管理员弹窗 -->
    <a-modal v-model:visible="addVisible" @cancel="handleCancel" @before-ok="handleOk" title="添加管理员">
      <a-form :model="addForm" ref="addFormRef">
        <a-form-item field="avatar" label="头像">
          <CmsUpload v-model="addForm.avatar">
            <!-- 上传自定义区域 -->
            <template #upload-trigger>
              <a-avatar :style="{ backgroundColor: '#14C9C9' }" shape="square" :image-url="addForm.avatar">
                <IconUser v-if="!addForm.avatar" />
                <template #trigger-icon>
                  <IconEdit />
                </template>
              </a-avatar>
            </template>
          </CmsUpload>
        </a-form-item>
        <a-form-item field="username" label="用户名" :rules="[{ required: true, message: '请输入用户名' }]">
          <a-input v-model="addForm.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item field="password" label="密码" :rules="[{ required: true, message: '请输入密码' }]">
          <a-input v-model="addForm.password" placeholder="请输入密码" />
        </a-form-item>

        <a-form-item field="nickname" label="呢称">
          <a-input v-model="addForm.nickname" placeholder="请输入呢称" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 重置密码弹窗 -->
    <a-modal v-model:visible="resetVisible" @cancel="handleResetCancel" @before-ok="handleResetOk" title="重置密码"
      class="reset-modal">
      <div class="user-info-box">
        <a-space>
          <a-avatar shape="square" :size="40" :image-url="resetForm.avatar" :key="resetForm.avatar">
            <icon-user v-if="!resetForm.avatar" />
          </a-avatar>
          <div class="user-detail">
            <div class="nickname">{{ resetForm.nickname }}</div>
            <div class="username">账号：{{ resetForm.username }}</div>
          </div>
        </a-space>
      </div>
      <a-form :model="resetForm" ref="resetFormRef">
        <a-form-item field="password" label="新密码" :rules="[{ required: true, message: '请输入新密码' }]">
          <a-input v-model="resetForm.password" placeholder="请输入新密码" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
// 请求接口
import { getList, addAdmin, edit, deleteAdmin } from '@/api/admin';
import { FormInstance } from '@arco-design/web-vue';

//页面参数
const pageConf = ref({
  page: 1,//当前页
  limit: 10,//每页条数
  limitOption: [10, 15, 20, 50, 100],//每页条数Option
  total: 0,//总条数
  fieldOption: [],//搜索字段Option
  field: '',//搜索字段
  keyword: '',//关键词
  timeArr: [] as Date[],//日期范围字符串
  time: "",//日期范围
  list: [],//列表数据
  tableLoading: false,//表格loading
})

const router = useRouter()
const route = useRoute()

// 初始化
const nextPage = (initPage = false) => {
  pageConf.value.tableLoading = true;
  if (initPage) {
    pageConf.value.page = 1;
    pageConf.value.limit = 10;
  }

  const params = {
    page: pageConf.value.page,
    limit: pageConf.value.limit,
    field: pageConf.value.field,
    keyword: pageConf.value.keyword,
    time: pageConf.value.time
  }

  // 更新路由参数
  router.push({
    query: params
  })

  getList(params).then((res) => {
    // 数据获取成功后 code= 200
    pageConf.value.list = res.data.list
    pageConf.value.total = res.data.total
    pageConf.value.limit = res.data.limit
    pageConf.value.fieldOption = res.data.fields
  }).finally(() => {
    pageConf.value.tableLoading = false;
  })
}

// 添加页码改变处理函数
const onPageChange = (page: number) => {
  pageConf.value.page = page;
  nextPage();
}

// 日期范围改变
const changeTime = (dates: any) => {
  pageConf.value.timeArr = dates;
  pageConf.value.time = dates.join(',');

  console.log(pageConf.value)
}

// 状态改变
const handleStatus = (record: any) => {
  console.log(record)
}

// 编辑
const handleEdit = (record: any) => {
  resetForm.value = {
    id: record.id,
    password: '',
    nickname: record.nickname,
    username: record.username,
    avatar: record.avatar
  };
  // 确保状态更新后再显示弹窗
  nextTick(() => {
    resetVisible.value = true;
  });
}

// 添加管理员相关
const addVisible = ref(false);
const addFormRef = ref<FormInstance>();
const addForm = ref({
  username: '',
  password: '',
  avatar: '',
  nickname: ''
});

// 打开添加管理员弹窗
const handleAdd = () => {
  addVisible.value = true;
};

// 取消添加
const handleCancel = () => {
  addForm.value = {
    username: '',
    password: '',
    avatar: '',
    nickname: ''
  };
};

// 确认添加
const handleOk = async (done: (closed: boolean) => void) => {
  await addAdmin(addForm.value).then((res) => {
    nextPage(true);
  })
};

// 重置密码相关
const resetVisible = ref(false);
const resetFormRef = ref<FormInstance>();
const resetForm = ref({
  id: '',
  password: '',
  nickname: '',
  username: '',
  avatar: ''
});

// 取消重置密码
const handleResetCancel = () => {
  resetForm.value = {
    id: '',
    password: '',
    nickname: '',
    username: '',
    avatar: ''
  };
};

// 确认重置密码
const handleResetOk = async (done: (closed: boolean) => void) => {
  await edit({
    id: resetForm.value.id,
    password: resetForm.value.password
  }).then((res) => {
    done(true);
  }).catch(() => {
    done(false);
  });
};

// 删除管理员
const handleDelete = async (record: any) => {
  try {
    await deleteAdmin({ id: record.id });
    Message.success('删除成功');
    nextPage(true);
  } catch (error) {
    Message.error('删除失败');
  }
}

//挂载
onMounted(() => {
  // 从路由参数中获取初始值
  const query = route.query
  if (Object.keys(query).length > 0) {
    pageConf.value.page = Number(query.page) || 1
    pageConf.value.limit = Number(query.limit) || 10
    pageConf.value.field = query.field as string || ''
    pageConf.value.keyword = query.keyword as string || ''
    pageConf.value.time = query.time as string || ''
    if (query.time && typeof query.time === 'string') {
      // 将字符串转换为日期数组
      const timeArr = query.time.split(',')
      pageConf.value.timeArr = timeArr.map(dateStr => new Date(dateStr))
    }
  }
  nextPage()
})

const formModel = ref({
  userId: '',
  keyword: '',
  dateRange: []
})

const pageConfig = ref({
  total: 100,
  pageSize: 10,
  current: 1,
  showTotal: true,
  showSizeChanger: true,
})

// 添加 TableColumnData 类型导入
import type { TableColumnData } from '@arco-design/web-vue';

// 修改 columns 的定义
const columns = ref<TableColumnData[]>([
  {
    title: 'ID',
    dataIndex: 'id',
    width: 100,
  },

  {
    title: '用户信息',
    slotName: 'userInfo',
    width: 220,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '登录账号',
    dataIndex: 'username',
    width: 220,
    tooltip: true,
    ellipsis: true,
  },
  {
    title: '状态',
    slotName: 'status',
    width: 120,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 220,
  },
  {
    title: '操作',
    slotName: 'action',
  },
])

const onSelect = (dates: any) => {
  console.log(dates)
}

const onOk = (dates: any) => {
  console.log(dates)
}

</script>

<style lang="less" scoped>
.page-container {
  padding: 10px;

  .box {
    padding: 10px;
    border-radius: 3px;
    width: 100%;
    background-color: var(--color-bg-1);
    min-height: calc(100vh - 160px);
  }

  .box-header {
    margin-bottom: 20px;
  }

  // 添加自定义表格样式
  :deep(.custom-table) {

    // 表头样式
    .arco-table-th {
      background-color: var(--color-fill-2);
      font-weight: 600;
      padding: 12px 16px;
    }

    // 单元格样式
    .arco-table-td {
      padding: 12px 16px;
    }

    // 条纹样式
    .arco-table-tr:nth-child(even) {
      background-color: var(--color-fill-1);
    }

    // hover效果
    .arco-table-tr:hover {
      td {
        background-color: var(--color-fill-2);
      }
    }
  }

  .avatar-preview {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px;
    }
  }
}
</style>
