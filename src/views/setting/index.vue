<template>
  <div class="page-container">
    <div class="box">
      <div class="box-header">
        <h2>系统配置</h2>
        <p>管理系统的基本配置参数</p>
      </div>

      <div class="box-main" v-loading="pageLoading">
        <a-form :model="formData" layout="vertical" v-if="!pageLoading">
          <a-row :gutter="24">
            <a-col :span="12" v-for="item in configList" :key="item.key">
              <a-form-item
                :field="item.key"
                :label="item.title"
                :help="`最后更新: ${item.updated_at}`"
              >
                <!-- 开关类型 -->
                <a-switch
                  v-if="item.data_type === 'int' && isBooleanConfig(item.key)"
                  v-model="formData[item.key]"
                  :checked-value="1"
                  :unchecked-value="0"
                  type="round"
                />

                <!-- 数字输入框 -->
                <a-input-number
                  v-else-if="item.data_type === 'int'"
                  v-model="formData[item.key]"
                  :min="0"
                  style="width: 100%"
                />

                <!-- 文本输入框 -->
                <a-input
                  v-else
                  v-model="formData[item.key]"
                  class="hover-input"
                >
                  <template #suffix>
                    <a-tooltip content="点击展开编辑">
                      <icon-edit style="cursor: pointer;" @click.stop="handleInput(item)" />
                    </a-tooltip>
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <div class="box-footer" v-if="!pageLoading">
          <a-button type="primary" :loading="saveLoading" @click="handleSave">
            <template #icon>
              <icon-save />
            </template>
            保存配置
          </a-button>
          <a-button @click="handleReset" style="margin-left: 12px">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 文本编辑模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="`编辑 ${currentItem?.title}`"
      @ok="handleModalOk"
      :hide-cancel="false"
      :width="800"
      ok-text="确定"
      cancel-text="取消"
    >
      <a-form :model="currentItem" layout="vertical">
        <a-form-item :field="currentItem?.key" :label="currentItem?.title">
          <a-textarea
            v-model="currentItem!.value"
            :auto-size="{ minRows: 10, maxRows: 25 }"
            v-if="currentItem"
            placeholder="请输入配置内容..."
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconEdit, IconSave, IconRefresh } from '@arco-design/web-vue/es/icon'

// 请求接口
import { getList, batchUpdate, type ConfigItem } from '@/api/setting'

// 响应式数据
const configList = ref<ConfigItem[]>([])
const formData = reactive<Record<string, any>>({})
const modalVisible = ref(false)
const currentItem = ref<ConfigItem | null>(null)
const pageLoading = ref(false)
const saveLoading = ref(false)
const originalData = ref<Record<string, any>>({})

// 判断是否为布尔类型的配置项
const isBooleanConfig = (key: string): boolean => {
  const booleanKeys = ['cache_enabled', 'comment_audit', 'comment_enabled']
  return booleanKeys.includes(key)
}

// 获取配置列表
const fetchConfigList = async () => {
  pageLoading.value = true
  try {
    const res = await getList()
    if (res && (res.code === 0 || res.code === 200)) {
      configList.value = res.data || []

      // 初始化表单数据
      configList.value.forEach(item => {
        formData[item.key] = item.formatted_value
        originalData.value[item.key] = item.formatted_value
      })
    } else {
      Message.error(res?.msg || '获取配置失败')
    }
  } catch (error) {
    Message.error('获取配置失败')
    console.error('获取配置失败:', error)
  } finally {
    pageLoading.value = false
  }
}

// 处理输入框点击（打开编辑模态框）
const handleInput = (item: ConfigItem) => {
  currentItem.value = { ...item, value: String(formData[item.key]) }
  modalVisible.value = true
}

// 处理模态框确认
const handleModalOk = () => {
  if (currentItem.value) {
    formData[currentItem.value.key] = currentItem.value.value
  }
  modalVisible.value = false
}

// 重置表单
const handleReset = () => {
  Object.keys(originalData.value).forEach(key => {
    formData[key] = originalData.value[key]
  })
  Message.info('已重置为原始值')
}

// 保存配置
const handleSave = async () => {
  saveLoading.value = true
  try {
    // 构建批量更新数据格式
    const updateData = configList.value.map(item => ({
      key: item.key,
      value: String(formData[item.key])
    }))

    const res = await batchUpdate({ configs: updateData })

    if (res && (res.code === 0 || res.code === 200)) {
      Message.success('保存成功')
      // 更新原始数据
      Object.keys(formData).forEach(key => {
        originalData.value[key] = formData[key]
      })
      // 重新获取最新数据
      await fetchConfigList()
    } else {
      Message.error(res?.msg || '保存失败')
    }
  } catch (error) {
    Message.error('保存失败')
    console.error('保存失败:', error)
  } finally {
    saveLoading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchConfigList()
})
</script>

<style lang="less" scoped>
.page-container {
  padding: 20px;

  .box {
    border-radius: 8px;
    width: 100%;
    background-color: var(--color-bg-1);
    min-height: calc(100vh - 160px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .box-header {
    padding: 24px 24px 0;
    border-bottom: 1px solid var(--color-border-2);
    margin-bottom: 0;

    h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--color-text-1);
    }

    p {
      margin: 0 0 24px 0;
      color: var(--color-text-3);
      font-size: 14px;
    }
  }

  .box-main {
    padding: 24px;
  }

  .box-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--color-border-2);
    background-color: var(--color-fill-1);
    border-radius: 0 0 8px 8px;
  }
}

// 表单项样式优化
:deep(.arco-form-item) {
  margin-bottom: 24px;

  .arco-form-item-label {
    font-weight: 500;
    color: var(--color-text-1);
  }

  .arco-form-item-help {
    color: var(--color-text-4);
    font-size: 12px;
    margin-top: 4px;
  }
}

// 悬停显示编辑图标
:deep(.arco-input-wrapper.hover-input) {
  .arco-input-suffix {
    display: none !important;
  }

  &:hover {
    .arco-input-suffix {
      display: inline-flex !important;
    }
  }
}

// 开关样式
:deep(.arco-switch) {
  &.arco-switch-checked {
    background-color: var(--color-primary-6);
  }
}

// 加载状态
.box-main[v-loading] {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
