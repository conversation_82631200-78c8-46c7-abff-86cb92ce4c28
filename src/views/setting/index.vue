<template>
  <div class="page-container">
    <div class="box">
      <div class="box-header">
        <h2>系统配置</h2>
        <p>管理系统的基本配置参数</p>
      </div>

      <div class="box-main" v-loading="pageLoading">
        <a-form :model="formData" layout="vertical" v-if="!pageLoading">
          <a-row :gutter="24">
            <a-col :span="12" v-for="item in configList" :key="item.key">
              <a-form-item
                :field="item.key"
                :label="item.title"
                :help="`最后更新: ${item.updated_at}`"
              >
                <!-- 开关类型 -->
                <a-switch
                  v-if="item.data_type === 'int' && isBooleanConfig(item.key)"
                  v-model="formData[item.key]"
                  :checked-value="1"
                  :unchecked-value="0"
                  type="round"
                />

                <!-- 数字输入框 -->
                <a-input-number
                  v-else-if="item.data_type === 'int'"
                  v-model="formData[item.key]"
                  :min="0"
                  style="width: 100%"
                />

                <!-- 文本输入框 -->
                <a-input
                  v-else
                  v-model="formData[item.key]"
                  class="hover-input"
                >
                  <template #suffix>
                    <a-tooltip content="点击展开编辑">
                      <icon-edit style="cursor: pointer;" @click.stop="handleInput(item)" />
                    </a-tooltip>
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <div class="box-footer" v-if="!pageLoading">
          <a-button type="primary" :loading="saveLoading" @click="handleSave">
            <template #icon>
              <icon-save />
            </template>
            保存配置
          </a-button>
          <a-button @click="handleReset" style="margin-left: 12px">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 文本编辑模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="`编辑 ${currentItem?.title}`"
      @ok="handleModalOk"
      :hide-cancel="false"
      :width="800"
      ok-text="确定"
      cancel-text="取消"
    >
      <a-form :model="currentItem" layout="vertical">
        <a-form-item :field="currentItem?.key" :label="currentItem?.title">
          <a-textarea
            v-model="currentItem!.value"
            :auto-size="{ minRows: 10, maxRows: 25 }"
            v-if="currentItem"
            placeholder="请输入配置内容..."
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 请求接口
import { getList, save } from '@/api/setting';

const list = ref<any[]>([])
const modalVisible = ref(false)
const currentItem = ref<any>(null)
const loading = ref(false)

const router = useRouter()
const route = useRoute()

//挂载
onMounted(() => {
  getList().then((res: any) => {
    if (res && res?.code == 200) {
      list.value = res.data
    }
  })
})

// 返回
const handleBack = () => {
  router.back()
}

// 处理输入框点击
const handleInput = (item: any) => {
  currentItem.value = item
  modalVisible.value = false
}

// 处理模态框确认
const handleModalOk = () => {
  modalVisible.value = false
  // 如果需要保存到后端，可以在这里调用 save 接口
  // save(currentItem.value).then((res: any) => {
  //   if (res?.code === 200) {
  //     // 保存成功
  //   }
  // })
}

// 保存
const handleSave = async () => {
  loading.value = true
  try {
    await save(list.value)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="less" scoped>
.page-container {
  padding: 10px;

  .h3 {
    font-weight: 300;
    font-size: 17px;
    line-height: 30px;
    margin-bottom: 15px;
    margin-top: 30px;
    color: var(--color-neutral-10);

    span {
      font-size: 12px;
      margin-left: 8px;
      color: var(--color-neutral-6);
    }
  }

  .box {
    padding: 10px;
    border-radius: 3px;
    width: 100%;
    background-color: var(--color-bg-1);
    min-height: calc(100vh - 160px);
  }

  .box-header {
    margin-bottom: 20px;
  }

  .box-main {
    padding: 20px;
    padding-top: 0;
  }
}

:deep(.arco-input-wrapper.hover-input) {
  .arco-input-suffix {
    display: none !important;
  }

  &:hover {
    .arco-input-suffix {
      display: inline-flex !important;
    }
  }
}
</style>
