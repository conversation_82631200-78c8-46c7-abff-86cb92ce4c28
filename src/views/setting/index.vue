<template>
  <div class="page-container">
    <div class="box">
    
      <div class="box-main">
        <template v-for="item in list" :key="item.id">
          <div class="h3">{{ item.title }}</div>
          <div class="box-content">
            <a-form :model="item" layout="vertical">
              <a-row :gutter="16">
                <a-col :span="6" v-for="childItem in item.items" :key="childItem.id">
                  <a-form-item :field="childItem.key" :label="childItem.title" label-col-flex="100px"
                    v-if="childItem.type == 'switch'">
                    <a-switch type="round" checked-value="1" unchecked-value="0" v-model="childItem.value" />
                  </a-form-item>
                  <a-form-item v-else :field="childItem.key" :label="childItem.title" label-col-flex="100px">
                    <a-input v-model="childItem.value" class="hover-input">
                      <template #suffix>
                        <a-tooltip content="点击展开编辑">
                          <icon-edit style="cursor: pointer;" @click.stop="handleInput(childItem)" />
                        </a-tooltip>
                      </template>
                    </a-input>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </template>
        <div class="box-footer">
          <a-button type="primary" :loading="loading" @click="handleSave">保存</a-button>
        </div>
      </div>
    </div>
    <a-modal v-model:visible="modalVisible" :title="currentItem?.title" @ok="handleModalOk" :hide-cancel="true"
      :footer-align="'right'" :width="800">
      <a-form :model="currentItem" layout="vertical">
        <a-form-item :field="currentItem?.key">
          <a-textarea v-model="currentItem!.value" :auto-size="{ minRows: 10, maxRows: 25 }" v-if="currentItem" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 请求接口
import { getList, save } from '@/api/setting';

const list = ref<any[]>([])
const modalVisible = ref(false)
const currentItem = ref<any>(null)
const loading = ref(false)

const router = useRouter()
const route = useRoute()

//挂载
onMounted(() => {
  getList().then((res: any) => {
    if (res && res?.code == 200) {
      list.value = res.data
    }
  })
})

// 返回
const handleBack = () => {
  router.back()
}

// 处理输入框点击
const handleInput = (item: any) => {
  currentItem.value = item
  modalVisible.value = false
}

// 处理模态框确认
const handleModalOk = () => {
  modalVisible.value = false
  // 如果需要保存到后端，可以在这里调用 save 接口
  // save(currentItem.value).then((res: any) => {
  //   if (res?.code === 200) {
  //     // 保存成功
  //   }
  // })
}

// 保存
const handleSave = async () => {
  loading.value = true
  try {
    await save(list.value)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="less" scoped>
.page-container {
  padding: 10px;

  .h3 {
    font-weight: 300;
    font-size: 17px;
    line-height: 30px;
    margin-bottom: 15px;
    margin-top: 30px;
    color: var(--color-neutral-10);

    span {
      font-size: 12px;
      margin-left: 8px;
      color: var(--color-neutral-6);
    }
  }

  .box {
    padding: 10px;
    border-radius: 3px;
    width: 100%;
    background-color: var(--color-bg-1);
    min-height: calc(100vh - 160px);
  }

  .box-header {
    margin-bottom: 20px;
  }

  .box-main {
    padding: 20px;
    padding-top: 0;
  }
}

:deep(.arco-input-wrapper.hover-input) {
  .arco-input-suffix {
    display: none !important;
  }

  &:hover {
    .arco-input-suffix {
      display: inline-flex !important;
    }
  }
}
</style>
