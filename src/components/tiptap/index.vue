<template>
    <div class="arco-editor-wrapper">
        <div class="arco-editor-toolbar" v-if="editor">
            <a-space wrap>
                <a-button-group>
                    <a-tooltip content="标题1">
                        <a-button size="small" @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
                            :type="editor.isActive('heading', { level: 1 }) ? 'primary' : 'secondary'">
                            <icon-h1 />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="标题2">
                        <a-tooltip content="标题2">
                            <a-button size="small" @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
                                :type="editor.isActive('heading', { level: 2 }) ? 'primary' : 'secondary'">
                                <icon-h2 />
                            </a-button>
                        </a-tooltip>
                    </a-tooltip>
                    <a-tooltip content="标题3">
                        <a-button size="small" @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
                            :type="editor.isActive('heading', { level: 3 }) ? 'primary' : 'secondary'">
                            <icon-h3 />
                        </a-button>
                    </a-tooltip>
                </a-button-group>

                <a-button-group>
                    <a-tooltip content="加粗">
                        <a-button size="small" @click="editor.chain().focus().toggleBold().run()"
                            :type="editor.isActive('bold') ? 'primary' : 'secondary'">
                            <icon-bold />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="斜体">
                        <a-button size="small" @click="editor.chain().focus().toggleItalic().run()"
                            :type="editor.isActive('italic') ? 'primary' : 'secondary'">
                            <icon-italic />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="删除线">
                        <a-button size="small" @click="editor.chain().focus().toggleStrike().run()"
                            :type="editor.isActive('strike') ? 'primary' : 'secondary'">
                            <icon-strikethrough />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="行内代码">
                        <a-button size="small" @click="editor.chain().focus().toggleCode().run()"
                            :type="editor.isActive('code') ? 'primary' : 'secondary'">
                            <icon-code />
                        </a-button>
                    </a-tooltip>
                </a-button-group>

                <a-button-group>
                    <a-tooltip content="左对齐">
                        <a-button size="small" @click="editor.chain().focus().setTextAlign('left').run()"
                            :type="editor.isActive({ textAlign: 'left' }) ? 'primary' : 'secondary'">
                            <icon-align-left />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="居中对齐">
                        <a-button size="small" @click="editor.chain().focus().setTextAlign('center').run()"
                            :type="editor.isActive({ textAlign: 'center' }) ? 'primary' : 'secondary'">
                            <icon-align-center />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="右对齐">
                        <a-button size="small" @click="editor.chain().focus().setTextAlign('right').run()"
                            :type="editor.isActive({ textAlign: 'right' }) ? 'primary' : 'secondary'">
                            <icon-align-right />
                        </a-button>
                    </a-tooltip>
                </a-button-group>

                <a-button-group>
                    <a-tooltip content="无序列表">
                        <a-button size="small" @click="editor.chain().focus().toggleBulletList().run()"
                            :type="editor.isActive('bulletList') ? 'primary' : 'secondary'">
                            <icon-unordered-list />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="有序列表">
                        <a-button size="small" @click="editor.chain().focus().toggleOrderedList().run()"
                            :type="editor.isActive('orderedList') ? 'primary' : 'secondary'">
                            <icon-ordered-list />
                        </a-button>
                    </a-tooltip>
                </a-button-group>

                <a-button-group>
                    <a-tooltip content="引用">
                        <a-button size="small" @click="editor.chain().focus().toggleBlockquote().run()"
                            :type="editor.isActive('blockquote') ? 'primary' : 'secondary'">
                            <icon-quote />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="代码块">
                        <a-button size="small" @click="editor.chain().focus().toggleCodeBlock().run()"
                            :type="editor.isActive('codeBlock') ? 'primary' : 'secondary'">
                            <icon-code-block />
                        </a-button>
                    </a-tooltip>
                </a-button-group>

                <a-button-group>
                    <a-tooltip content="撤销">
                        <a-button size="small" @click="editor.chain().focus().undo().run()"
                            :disabled="!editor.can().undo()">
                            <icon-undo />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="重做">
                        <a-button size="small" @click="editor.chain().focus().redo().run()"
                            :disabled="!editor.can().redo()">
                            <icon-redo />
                        </a-button>
                    </a-tooltip>
                </a-button-group>

                <a-button-group>
                    <a-tooltip content="全屏">
                        <a-button size="small" @click="toggleFullscreen" :type="isFullscreen ? 'primary' : 'secondary'">
                            <icon-fullscreen-exit v-if="isFullscreen" />
                            <icon-fullscreen v-else />
                        </a-button>
                    </a-tooltip>
                    <a-tooltip content="源代码">
                        <a-button size="small" @click="toggleHtmlMode" :type="isHtmlMode ? 'primary' : 'secondary'">
                            <icon-code />
                        </a-button>
                    </a-tooltip>
                </a-button-group>

                <a-tooltip content="插入链接">
                    <a-button size="small" @click="addLink">
                        <icon-link />
                    </a-button>
                </a-tooltip>

                <a-tooltip content="插入图片">
                    <a-button size="small" @click="openImageUpload">
                        <icon-image />
                    </a-button>
                </a-tooltip>
            </a-space>
        </div>
        <editor-content :editor="editor" class="arco-editor-content" />

        <div v-if="isHtmlMode" class="html-editor">
            <a-textarea v-model="htmlContent" :auto-size="{ minRows: 10, maxRows: 20 }" @change="updateHtmlContent" />
        </div>

        <!-- 链接对话框 -->
        <a-modal v-model:visible="linkModalVisible" title="插入链接" @ok="handleLinkConfirm" @cancel="handleLinkCancel"
            :mask-closable="false" :ok-button-props="{ disabled: !linkForm.url }">
            <a-form :model="linkForm" layout="vertical" ref="linkFormRef">
                <a-form-item label="链接地址" field="url" :rules="[{ required: true, message: '请输入链接地址' }]">
                    <a-input v-model="linkForm.url" placeholder="请输入链接地址" allow-clear />
                </a-form-item>
                <a-form-item label="链接文本" field="text">
                    <a-input v-model="linkForm.text" placeholder="请输入链接文本（可选）" allow-clear />
                </a-form-item>
                <a-form-item label="在新窗口打开" field="openInNewTab">
                    <a-switch v-model="linkForm.openInNewTab" />
                </a-form-item>
            </a-form>
        </a-modal>

        <!-- 图片上传对话框 -->
        <a-modal v-model:visible="imageModalVisible" title="插入图片" @ok="handleImageConfirm" @cancel="handleImageCancel"
            :mask-closable="false" :ok-button-props="{ disabled: !imageForm.url && !imageForm.file }">
            <a-form :model="imageForm" layout="vertical" ref="imageFormRef">
                <a-form-item label="上传图片" field="file">
                    <CmsUpload v-model="imageForm.url" :show-file-list="false" accept="image/*">
                        <template #upload-trigger>
                            <div v-if="!imageForm.url"
                                style="display: flex; align-items: center; justify-content: center; flex-direction: column; gap: 10px;border: 1px dashed var(--color-border);border-radius: 4px;padding: 20px;text-align: center;cursor: pointer;">
                                <icon-upload />
                                <div>点击或拖拽图片至此处上传</div>
                            </div>
                            <div v-else>
                                <img :src="imageForm.url" class="upload-demo-preview" />
                            </div>
                        </template>
                    </CmsUpload>
                </a-form-item>
                <a-form-item label="图片地址" field="url">
                    <a-input v-model="imageForm.url" placeholder="或直接输入图片链接" allow-clear />
                </a-form-item>
                <a-form-item label="图片描述" field="alt">
                    <a-input v-model="imageForm.alt" placeholder="请输入图片描述（可选）" allow-clear />
                </a-form-item>
                <a-form-item label="图片对齐方式" field="align">
                    <a-radio-group v-model="imageForm.align">
                        <a-radio value="left">左对齐</a-radio>
                        <a-radio value="center">居中</a-radio>
                        <a-radio value="right">右对齐</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="图片尺寸" field="size">
                    <a-space direction="vertical" :size="12" fill>
                        <a-radio-group v-model="imageForm.sizeType">
                            <a-radio value="pixel">像素</a-radio>
                            <a-radio value="percent">百分比</a-radio>
                        </a-radio-group>
                        <a-space>
                            <a-input-number v-model="imageForm.width" placeholder="宽度" style="width: 120px" :min="1"
                                :max="imageForm.sizeType === 'percent' ? 100 : 9999">
                                <template #append>{{ imageForm.sizeType === 'pixel' ? 'px' : '%' }}</template>
                            </a-input-number>
                            <span>×</span>
                            <a-input-number v-model="imageForm.height" placeholder="高度" style="width: 120px" :min="1"
                                :max="imageForm.sizeType === 'percent' ? 100 : 9999">
                                <template #append>{{ imageForm.sizeType === 'pixel' ? 'px' : '%' }}</template>
                            </a-input-number>
                            <a-button @click="resetImageSize">重置尺寸</a-button>
                        </a-space>
                    </a-space>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import { Message } from '@arco-design/web-vue'
import {
    IconBold, IconItalic, IconStrikethrough, IconCode,
    IconH1, IconH2, IconH3,
    IconUnorderedList, IconOrderedList,
    IconUndo, IconRedo,
    IconAlignLeft, IconAlignCenter, IconAlignRight,
    IconQuote, IconCodeBlock, IconLink,
    IconImage, IconUpload,
    IconFullscreen, IconFullscreenExit
} from '@arco-design/web-vue/es/icon'
import { upload } from '@/api/basis'

// 定义 props 和 emit
const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['update:modelValue'])

const editor = useEditor({
    content: props.modelValue,
    extensions: [
        StarterKit.configure({
            heading: {
                levels: [1, 2, 3]
            }
        }),
        TextAlign.configure({
            types: ['heading', 'paragraph']
        }),
        Link.configure({
            openOnClick: false,
            HTMLAttributes: {
                class: 'editor-link'
            }
        }),
        Image.configure({
            inline: true,
            HTMLAttributes: {
                class: 'editor-image'
            }
        })
    ],
    onUpdate: ({ editor }) => {
        emit('update:modelValue', editor.getHTML())
    },
    editorProps: {
        attributes: {
            class: 'arco-editor-input'
        }
    }
})

// 链接相关
const linkModalVisible = ref(false)
const linkFormRef = ref(null)
const linkForm = reactive({
    url: '',
    text: '',
    openInNewTab: true
})

const addLink = () => {
    const selectedText = editor.value?.state.selection.content().content?.firstChild?.text || ''
    linkForm.text = selectedText
    linkForm.url = ''
    linkModalVisible.value = true
}

const handleLinkConfirm = async () => {
    const { validate } = linkFormRef.value as any
    try {
        await validate()
        if (editor.value) {
            const attrs = {
                href: linkForm.url,
                target: linkForm.openInNewTab ? '_blank' : null,
                rel: linkForm.openInNewTab ? 'noopener noreferrer' : null
            }

            if (linkForm.text && !editor.value.state.selection.content().size) {
                editor.value.chain()
                    .focus()
                    .insertContent({
                        type: 'text',
                        text: linkForm.text,
                        marks: [{
                            type: 'link',
                            attrs
                        }]
                    })
                    .run()
            } else {
                editor.value.chain()
                    .focus()
                    .setLink(attrs)
                    .run()
            }
        }
        linkModalVisible.value = false
    } catch (error) {
        // 表单验证失败
    }
}

const handleLinkCancel = () => {
    linkModalVisible.value = false
}

// 图片相关
const imageModalVisible = ref(false)
const imageFormRef = ref(null)
const imagePreview = ref('')
const imageForm = reactive({
    file: null,
    url: '',
    alt: '',
    align: 'center',
    sizeType: 'pixel',
    width: 0,
    height: 0,
    originalWidth: 0,
    originalHeight: 0
})

const openImageUpload = () => {
    imageForm.file = null
    imageForm.url = ''
    imageForm.alt = ''
    imageForm.width = 0
    imageForm.height = 0
    imageForm.originalWidth = 0
    imageForm.originalHeight = 0
    imagePreview.value = ''
    imageModalVisible.value = true
}



interface CustomRequestOptions {
    file: File;
    onProgress?: (percent: number) => void;
    onSuccess?: (response: any) => void;
    onError?: (error: Error) => void;
}

const handleImageUpload = async ({ file }: CustomRequestOptions) => {
    try {
        if (!file) {
            throw new Error('未选择文件');
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'image');

        const response = await upload(formData);
        if (response.data) {
            imagePreview.value = response.data.url
            imageForm.url = response.data.url
            Message.success('图片上传成功')
        } else {
            throw new Error('上传失败')
        }
    } catch (error: any) {
        Message.error('图片上传失败：' + (error.message || '未知错误'))
        console.error('图片上传错误：', error)
    }
}

const handleImageConfirm = () => {
    if (!imageForm.url) {
        Message.error('请上传图片或输入图片地址')
        return
    }

    if (editor.value) {
        const getSize = (value: number, type: string) => {
            if (!value) return ''
            return type === 'pixel' ? `${value}px` : `${value}%`
        }

        const style = [
            imageForm.width ? `width: ${getSize(imageForm.width, imageForm.sizeType)}` : '',
            imageForm.height ? `height: ${getSize(imageForm.height, imageForm.sizeType)}` : '',
            imageForm.align === 'center' ? 'display: block; margin-left: auto; margin-right: auto' : '',
            imageForm.align === 'left' ? 'float: left; margin-right: 1em' : '',
            imageForm.align === 'right' ? 'float: right; margin-left: 1em' : ''
        ].filter(Boolean).join('; ')

        editor.value.chain()
            .focus()
            .setImage({
                src: imageForm.url,
                alt: imageForm.alt,
            })
            .run()
    }
    imageModalVisible.value = false
}

const handleImageCancel = () => {
    imageModalVisible.value = false
}

const resetImageSize = () => {
    imageForm.sizeType = 'pixel'
    imageForm.width = imageForm.originalWidth
    imageForm.height = imageForm.originalHeight
}

// 添加全屏和源代码按钮
const isFullscreen = ref(false)
const isHtmlMode = ref(false)
const htmlContent = ref('')

// 添加全屏切换方法
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
    const wrapper = document.querySelector('.arco-editor-wrapper')
    if (isFullscreen.value) {
        wrapper?.classList.add('fullscreen')
    } else {
        wrapper?.classList.remove('fullscreen')
    }
}

// 添加源代码模式切换方法
const toggleHtmlMode = () => {
    isHtmlMode.value = !isHtmlMode.value
    if (isHtmlMode.value) {
        htmlContent.value = editor.value?.getHTML() || ''
    }
}

// 添加源代码更新方法
const updateHtmlContent = () => {
    if (editor.value && htmlContent.value) {
        editor.value.commands.setContent(htmlContent.value)
    }
}

// 监听外部 modelValue 变化
watch(
    () => props.modelValue,
    (newContent) => {
        // 只有当编辑器内容与新值不同时才更新
        if (editor.value && newContent !== editor.value.getHTML()) {
            editor.value.commands.setContent(newContent)
        }
    }
)
</script>

<style scoped>
.arco-editor-wrapper {
    border: 1px solid var(--color-border);
    border-radius: 4px;
}

.arco-editor-toolbar {
    padding: 8px;
    border-bottom: 1px solid var(--color-border);
    background-color: var(--color-bg-2);
    position: sticky;
    top: 0;
    z-index: 1;
}

.arco-editor-content {
    padding: 12px;
}

.arco-editor-input {
    outline: none;
}

.upload-demo {
    width: 100%;
    border: 1px dashed var(--color-border);
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
}

.upload-demo:hover {
    border-color: var(--color-primary-6);
}

.upload-demo-text {
    color: var(--color-text-3);
}

.upload-demo-preview {
    max-width: 100%;
    max-height: 200px;
    object-fit: contain;
}

:deep(.ProseMirror) {
    min-height: 600px;

    &:focus {
        outline: none;
        box-shadow: none;
    }
}

:deep(.ProseMirror h1) {
    font-size: 2em;
    margin: 0.67em 0;
}

:deep(.ProseMirror h2) {
    font-size: 1.5em;
    margin: 0.75em 0;
}

:deep(.ProseMirror h3) {
    font-size: 1.17em;
    margin: 0.83em 0;
}

:deep(.ProseMirror ul) {
    padding-left: 1.2em;
}

:deep(.ProseMirror ol) {
    padding-left: 1.2em;
}

:deep(.ProseMirror code) {
    background-color: var(--color-fill-2);
    border-radius: 2px;
    padding: 0.2em 0.4em;
    font-family: monospace;
}

:deep(.ProseMirror blockquote) {
    border-left: 4px solid var(--color-neutral-3);
    margin: 0;
    padding-left: 1em;
    font-style: italic;
}

:deep(.ProseMirror pre) {
    background: var(--color-neutral-1);
    border-radius: 4px;
    padding: 0.75em 1em;
    margin: 0.5em 0;
}

:deep(.editor-link) {
    color: var(--color-primary-6);
    text-decoration: none;
    cursor: pointer;
}

:deep(.editor-link:hover) {
    text-decoration: underline;
}

:deep(.editor-image) {
    max-width: 100%;
    height: auto;
    margin: 1em 0;
    border-radius: 4px;
}

.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: var(--color-bg-1);
    display: flex;
    flex-direction: column;
}

.fullscreen .arco-editor-content {
    flex: 1;
    overflow-y: auto;
}

.fullscreen .html-editor {
    flex: 1;
    overflow-y: auto;
}

.html-editor {
    padding: 12px;
    border-top: 1px solid var(--color-border);
}
</style>