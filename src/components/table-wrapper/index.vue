<template>
  <a-table
    :bordered="{ cell: true }"
    :stripe="true"
    :hover="true"
    :pagination="{
      showTotal: true,
      showJumper: true,
      baseSize: 'small',
      showPageSize: true,
      pageSizeOptions: [10, 20, 50, 100],
      formatTotal: (total) => `共 ${total} 条`,
      formatPageSize: (size) => `${size} 条/页`,
    }"
    v-bind="$attrs"
  >
    <template v-for="item in Object.keys($slots)" #[item]="data" :key="item">
      <slot :name="item" v-bind="data"></slot>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'TableWrapper',
});
</script>

<style lang="less" scoped>
:deep(.arco-table) {
  // 表头样式
  .arco-table-th {
    background-color: var(--color-fill-2);
    font-weight: 600;
    &:hover {
      background-color: var(--color-fill-3);
    }
  }

  // 斑马纹样式
  .arco-table-tr:nth-child(even) {
    background-color: var(--color-fill-1);
  }

  // 悬浮样式
  .arco-table-tr:hover {
    td {
      background-color: var(--color-fill-2);
    }
  }

  // 单元格内边距
  .arco-table-td,
  .arco-table-th {
    padding: 8px 16px;
  }

  // 分页器样式
  .arco-pagination {
    margin-top: 16px;
    justify-content: flex-end;
  }
}
</style> 