<template>
    <div class="cms-upload">
        <a-spin :loading="loading">
            <a-upload :show-file-list="false" :accept="accept" :disabled="disabled" :multiple="false" :draggable="true"
                :custom-request="customRequest">
                <!-- 当没有设置 upload-trigger 插槽时显示默认内容 -->
                <template #upload-button v-if="hasUploadTrigger">
                    <slot name="upload-trigger">
                    </slot>
                </template>
                <template #upload-button v-else>
                    <a-button type="primary">
                        <template #icon>
                            <icon-upload />
                        </template>
                        立即上传
                    </a-button>
                </template>
            </a-upload>
        </a-spin>
    </div>
</template>

<script setup lang="ts">
import { ref, useSlots, computed } from 'vue'
import { IconUpload } from '@arco-design/web-vue/es/icon'
import { upload } from '@/api/basis'

interface Props {
    modelValue: any
    accept: string
    disabled: boolean
    maxSize: number
    fileType: string
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: null,
    accept: '',
    disabled: false,
    maxSize: 10 * 1024, // 默认10MB
    fileType: 'image', // 默认图片
})

const emit = defineEmits()

const loading = ref(false)

const slots = useSlots()
const hasUploadTrigger = computed(() => !!slots['upload-trigger'])

const customRequest = async (option: any) => {
    const { onProgress, onError, onSuccess, fileItem, name } = option
    console.log(fileItem.file)
    // 创建 FormData 对象
    const formData = new FormData()
    formData.append('file', fileItem.file)
    formData.append('type', props.fileType)
    await upload(formData).then((res) => {
        emit('update:modelValue', res.data.fileUrl)
    })

}
</script>

<style lang="less" scoped>
.cms-upload {
    display: inline-block;
}
</style>