/**
 * 通用验证工具函数
 */

// 手机号验证
export const isValidPhone = (phone: string): boolean => {
  const reg = /^1[3-9]\d{9}$/;
  return reg.test(phone);
};

// 邮箱验证
export const isValidEmail = (email: string): boolean => {
  const reg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
  return reg.test(email);
};

// 身份证号验证
export const isValidIDCard = (idCard: string): boolean => {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return reg.test(idCard);
};

// 密码强度验证 (至少8位，包含大小写字母和数字)
export const isStrongPassword = (password: string): boolean => {
  const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
  return reg.test(password);
};

// URL验证
export const isValidURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// 银行卡号验证（简单版本：13-19位数字）
export const isValidBankCard = (cardNo: string): boolean => {
  const reg = /^\d{13,19}$/;
  return reg.test(cardNo);
};

// 中文姓名验证（2-10个汉字）
export const isValidChineseName = (name: string): boolean => {
  const reg = /^[\u4e00-\u9fa5]{2,10}$/;
  return reg.test(name);
};

// 接口响应类型
export interface ApiResponse<T = any> {
  code?: number;
  data?: T;
  message?: string;
}

// 验证接口响应是否成功
export const isValidResponse = (response: any): response is ApiResponse => {
  return response && typeof response === 'object' && 'code' in response;
};

// 验证接口响应是否成功且状态码为200
export const isSuccessResponse = (response: any): boolean => {
  return isValidResponse(response) && response.code === 200;
}; 