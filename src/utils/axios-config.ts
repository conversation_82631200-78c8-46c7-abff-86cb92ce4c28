import axios from 'axios';
import { Notification, Message } from '@arco-design/web-vue';
import useLoading from '@/hooks/loading';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import useUserStore from '@/store/modules/user';
import router from '@/router';
// 配置 NProgress
NProgress.configure({
  showSpinner: false, // 是否显示加载微调器
  minimum: 0.1, // 最小百分比
  easing: 'ease', // 动画方式
  speed: 200, // 动画速度
});

// 创建axios实例
const instance = axios.create({
  baseURL: import.meta.env.VITE_AXIOS_BASE_URL || '/api', // 设置基础URL
  timeout: 5000, // 请求超时时间
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 设置Content-Type
    config.headers['Content-Type'] = 'application/json';

    // 检查是否需要显示加载状态
    if (config.data && !config.data.closeLoad) {
      // 这里可以添加显示加载状态的逻辑
      // 开启进度条
      NProgress.start();
    }

    return config;
  },
  (error) => {
    NProgress.done(); // 发生错误时结束进度条
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 完成进度条
    NProgress.done();

    // 处理加载状态
    if (response.config.data) {
      const requestData =
        typeof response.config.data === 'string'
          ? JSON.parse(response.config.data)
          : response.config.data;

      if (!requestData.closeLoad) {
        // 这里可以添加隐藏加载状态的逻辑
        // 例如：隐藏全局loading
      }
    }

    const { data } = response;

    // 处理HTTP状态码为200的情况
    if (response.status === 200) {
      // 如果后端返回的数据结构包含code字段，按原逻辑处理
      if (data && typeof data === 'object' && 'code' in data) {
        const msg = data.msg || data.message || '';
        const code = data.code;

        switch (code) {
          case 200:
          case 0: // 有些API成功时返回0
            if (msg && msg !== 'success' && msg !== 'ok') {
              Notification.success({
                title: '提示',
                content: msg,
                duration: 3000,
                closable: true,
              });
            }
            return Promise.resolve(data);
          case 401:
            // 关闭其他提示
            Message.clear();
            // 添加401未授权的统一提示
            Message.error(msg || '登录已过期，请重新登录');
            router.push('/login');
            return Promise.reject(data);
          default:
            // 业务错误
            const errorMsg = msg || '请求失败';
            Message.error(errorMsg);
            return Promise.reject(data);
        }
      } else {
        // 如果没有code字段，直接返回数据（适配不同的API格式）
        return Promise.resolve(data);
      }
    }

    return Promise.resolve(data);
  },
  (error) => {
    // 发生错误时结束进度条
    NProgress.done();

    // 发生错误时也需要隐藏加载状态
    if (error.config && error.config.data) {
      const requestData =
        typeof error.config.data === 'string'
          ? JSON.parse(error.config.data)
          : error.config.data;

      if (!requestData.closeLoad) {
        // 隐藏加载状态
        useLoading(false);
      }
    }

    let errorMessage = '请求失败';

    // 对响应错误做点什么
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          errorMessage = '登录已过期，请重新登录';
          Message.error(errorMessage);
          router.push('/login');
          return Promise.reject(error);
        case 403:
          errorMessage = '权限不足';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        default:
          errorMessage = data?.msg || data?.message || `请求失败 (${status})`;
      }
    } else if (error.request) {
      errorMessage = '网络连接失败，请检查网络';
    } else {
      errorMessage = error.message || '请求配置错误';
    }

    // 显示错误提示
    Message.error(errorMessage);

    return Promise.reject(error);
  }
);

export default instance;
