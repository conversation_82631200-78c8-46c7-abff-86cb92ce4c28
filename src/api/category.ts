import axios from '../utils/axios-config';

// 栏目分类接口类型定义
export interface CategoryItem {
  statusLoading: boolean;
  id: number;
  name: string;
  slug: string;
  parent_id: number;
  template: string | null;
  keywords: string | null;
  description: string | null;
  sort_order: number;
  status: number;
  children?: CategoryItem[];
}

export interface CategoryListResponse {
  list: CategoryItem[];
  total: number;
  limit: number;
  pages: number;
}

export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

export interface CategoryFormData {
  name: string;
  slug: string;
  parent_id: number;
  template?: string;
  keywords?: string;
  description?: string;
  sort_order: number;
  status: number;
}

// 获取栏目列表
export const getCategoryList = (params: any = {}) => {
  return axios.post<ApiResponse<CategoryListResponse>>('/category/index', params);
};

// 获取单个栏目详情
export const getCategoryDetail = (id: number) => {
  return axios.get<ApiResponse<CategoryItem>>(`/category/${id}`);
};

// 新增栏目
export const createCategory = (data: CategoryFormData) => {
  return axios.post<ApiResponse<any>>('/category/store', data);
};

// 更新栏目
export const updateCategory = (id: number, data: CategoryFormData) => {
  return axios.put<ApiResponse<any>>(`/category/${id}`, data);
};

// 删除栏目
export const deleteCategory = (id: number) => {
  return axios.delete<ApiResponse<any>>(`/category/${id}`);
};

// 批量删除栏目
export const batchDeleteCategory = (ids: number[]) => {
  return axios.post<ApiResponse<any>>('/category/batch-delete', { ids });
};

// 更新栏目状态
export const updateCategoryStatus = (id: number, status: number) => {
  return axios.post<ApiResponse<any>>('/category/status', { id, status });
};

// 更新栏目排序
export const updateCategorySort = (id: number, sort_order: number) => {
  return axios.post<ApiResponse<any>>('/category/sort', { id, sort_order });
};

