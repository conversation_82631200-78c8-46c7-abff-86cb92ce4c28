import axios from '../utils/axios-config';

// 登录 - 添加mock功能用于开发测试
export const login = (data: any) => {
  // 简单的mock登录逻辑，实际项目中应该调用真实API
  if (data.username === 'admin' && data.password === '123123') {
    return Promise.resolve({
      code: 200,
      msg: '登录成功',
      data: {
        token: 'mock-token-' + Date.now(),
        user: {
          role: 'admin',
          nickname: 'Admin User',
          avatar: ''
        }
      }
    });
  } else {
    return Promise.reject('用户名或密码错误');
  }

  // 真实API调用（注释掉用于开发测试）
  // return axios.post<any>('/admin/login/pass', data);
};

// 获取用户信息
export function getUserInfo() {
  return axios.get<any>('/admin/login/pass');
}

// 退出登录
export function logout() {
  return axios.post<any>('/admin/user/logout');
}
