import axios from '../utils/axios-config';

// 配置项接口定义
export interface ConfigItem {
  key: string;
  title: string;
  value: string;
  data_type: string;
  updated_at: string;
  formatted_value: any;
}

// 批量更新配置项接口
export interface BatchUpdateItem {
  key: string;
  value: string;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 获取配置列表
 * Route::any('/setting/index', [SettingController::class, 'index'])
 * @param data 查询参数
 * @returns Promise<ApiResponse<ConfigItem[]>>
 */
export const getList = (data: any = {}) => {
  return axios.post<ApiResponse<ConfigItem[]>>('/setting/index', data);
};

/**
 * 获取配置分组
 * Route::any('/setting/groups', [SettingController::class, 'groups'])
 * @param data 查询参数
 * @returns Promise<ApiResponse<any>>
 */
export const getGroups = (data: any = {}) => {
  return axios.post<ApiResponse<any>>('/setting/groups', data);
};

/**
 * 修改单个配置
 * Route::post('/setting/update', [SettingController::class, 'store'])
 * @param data 配置数据 { key: string, value: string }
 * @returns Promise<ApiResponse<any>>
 */
export const save = (data: { key: string; value: string }) => {
  return axios.post<ApiResponse<any>>('/setting/update', data);
};

/**
 * 显示单个配置
 * Route::get('/setting/{key}', [SettingController::class, 'show'])
 * @param key 配置键名
 * @returns Promise<ApiResponse<ConfigItem>>
 */
export const getConfigByKey = (key: string) => {
  return axios.get<ApiResponse<ConfigItem>>(`/setting/${key}`);
};

/**
 * 批量修改配置
 * Route::post('/setting/batch-update', [SettingController::class, 'batchUpdate'])
 * @param data 批量配置数据 { configs: BatchUpdateItem[] }
 * @returns Promise<ApiResponse<any>>
 */
export const batchUpdate = (data: { configs: BatchUpdateItem[] }) => {
  return axios.post<ApiResponse<any>>('/setting/batch-update', data);
};

/**
 * 删除配置
 * Route::delete('/setting/delete', [SettingController::class, 'delete'])
 * @param data 删除参数 { key: string } 或 { keys: string[] }
 * @returns Promise<ApiResponse<any>>
 */
export const deleteConfig = (data: { key?: string; keys?: string[] }) => {
  return axios.delete<ApiResponse<any>>('/setting/delete', { data });
};
