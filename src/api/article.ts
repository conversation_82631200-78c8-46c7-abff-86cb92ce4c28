import axios from '../utils/axios-config';

// 列表
export const getList = (data: any = {}) => {
  return axios.post<any>('/admin/forum/index', data);
};

// 详情
export const getInfo = (data: any = {}) => {
  return axios.post<any>('/admin/forum/detail', data);
};

// 新增&编辑
export const save = (data: any = {}) => {
  return axios.post<any>('/admin/forum/update', data);
};

// 删除
export const deleteArticle = (data: any = {}) => {
  return axios.post<any>('/admin/forum/delete', data);
};

//帖子分类
export const getCategoryList = (data: any = {}) => {
  return axios.post<any>('/admin/forum/CategoryList');
};
