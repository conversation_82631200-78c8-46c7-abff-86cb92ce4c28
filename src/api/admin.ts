import axios from '../utils/axios-config';

// 列表
export const getList = (data: any) => {
  return axios.post<any>('/admin/user/index', data);
};

// 添加管理员
export const addAdmin = (data: any) => {
  return axios.post<any>('/admin/user/add', data);
};

// 改密
export const edit = (data: any) => {
  return axios.post<any>('/admin/user/edit', data);
};

// 删除
export const deleteAdmin = (data: any) => {
  return axios.post<any>('/admin/user/del', data);
};
