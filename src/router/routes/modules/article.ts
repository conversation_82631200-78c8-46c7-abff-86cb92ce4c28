import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const Article: AppRouteRecordRaw = {
  path: '/article',
  name: 'article',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '文章',
    requiresAuth: true,
    icon: 'icon-find-replace',
    order: 1,
  },
  children: [
    {
      path: '/article/list',
      name: 'ArticleList',
      component: () => import('@/views/article/index.vue'),
      meta: {
        locale: '列表',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: '/article/details',
      name: 'ArticleDetails',
      component: () => import('@/views/article/details.vue'),
      meta: {
        locale: '详情',
        hideInMenu: true,
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default Article;
