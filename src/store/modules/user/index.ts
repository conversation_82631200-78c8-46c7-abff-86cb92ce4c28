import { defineStore } from 'pinia';
import { login as userLogin, logout as userLogout } from '@/api/user';
import { setToken, clearToken } from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import { UserState } from './types';
import useAppStore from '../app';
const useUserStore = defineStore('user', {
  state: (): UserState => ({
    avatar: undefined,
    role: '',
    nickname: '',
    token: '',
  }),
  getters: {
    userInfo(state: UserState): UserState {
      return { ...state };
    },
  },

  actions: {
    switchRoles() {
      return new Promise((resolve) => {
        this.role = this.role === 'user' ? 'admin' : 'user';
        resolve(this.role);
      });
    },
    // Set user's information
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    // Get user's information
    async info() {
      // 如果已经有用户信息，直接返回
      if (this.role) {
        return Promise.resolve(this.userInfo);
      }

      // 模拟获取用户信息，实际项目中应该调用API
      // const res = await getUserInfo();

      // 临时设置默认用户信息，避免路由守卫失败
      this.setInfo({
        role: 'admin',
        nickname: 'Admin User',
        avatar: ''
      });

      return Promise.resolve(this.userInfo);
    },

    async login(loginForm: any) {
      try {
        const res: any = await userLogin(loginForm);
        if (res.code === 200) {
          setToken(res.data.token);
          this.setInfo(res.data.user);
          return Promise.resolve(res.data);
        } else {
          return Promise.reject(res.msg);
        }
      } catch (error) {
        return Promise.reject(error);
      }
    },
    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      clearToken();
      removeRouteListener();
    },
    // Logout
    async logout() {
      try {
        await userLogout();
      } finally {
        this.logoutCallBack();
      }
    },
  },
  persist: true,
});

export default useUserStore;
