import { defineStore } from 'pinia';
import { login as userLogin, logout as userLogout } from '@/api/user';
import { setToken, clearToken } from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import { UserState } from './types';
import useAppStore from '../app';
const useUserStore = defineStore('user', {
  state: (): UserState => ({
    avatar: undefined,
    role: '',
    nickname: '',
    token: '',
  }),
  getters: {
    userInfo(state: UserState): UserState {
      return { ...state };
    },
  },

  actions: {
    switchRoles() {
      return new Promise((resolve) => {
        this.role = this.role === 'user' ? 'admin' : 'user';
        resolve(this.role);
      });
    },
    // Set user's information
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    // Get user's information
    async info() {
      // 如果已经有用户信息，直接返回
      if (this.role) {
        return Promise.resolve(this.userInfo);
      }

      // 模拟获取用户信息，实际项目中应该调用API
      // const res = await getUserInfo();

      // 临时设置默认用户信息，避免路由守卫失败
      this.setInfo({
        role: 'admin',
        nickname: 'Admin User',
        avatar: ''
      });

      return Promise.resolve(this.userInfo);
    },

    async login(loginForm: any) {
      try {
        const res: any = await userLogin(loginForm);

        // 处理不同的API响应格式
        if (res) {
          // 如果响应包含code字段
          if ('code' in res) {
            if (res.code === 200 || res.code === 0) {
              const token = res.data?.token || res.token;
              const userInfo = res.data?.user || res.data || {};

              if (token) {
                setToken(token);
              }

              // 设置用户信息
              this.setInfo({
                role: userInfo.role || 'admin',
                nickname: userInfo.nickname || userInfo.name || 'Admin User',
                avatar: userInfo.avatar || '',
                token: token
              });

              return Promise.resolve(res);
            } else {
              return Promise.reject(res.msg || res.message || '登录失败');
            }
          } else {
            // 如果响应不包含code字段，直接处理数据
            const token = res.token || res.access_token;
            const userInfo = res.user || res;

            if (token) {
              setToken(token);

              // 设置用户信息
              this.setInfo({
                role: userInfo.role || 'admin',
                nickname: userInfo.nickname || userInfo.name || 'Admin User',
                avatar: userInfo.avatar || '',
                token: token
              });

              return Promise.resolve(res);
            } else {
              return Promise.reject('登录响应中未找到token');
            }
          }
        } else {
          return Promise.reject('登录响应为空');
        }
      } catch (error: any) {
        const errorMsg = error?.response?.data?.message ||
                        error?.response?.data?.msg ||
                        error?.message ||
                        '登录失败';
        return Promise.reject(errorMsg);
      }
    },
    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      clearToken();
      removeRouteListener();
    },
    // Logout
    async logout() {
      try {
        await userLogout();
      } finally {
        this.logoutCallBack();
      }
    },
  },
  persist: true,
});

export default useUserStore;
